<?php
require '../config/conn.php';

// قراءة إعدادات Vodafone Cash من الملف المشترك
require '../config/vodafone_cash_config.php';

// جلب طرق الدفع المتاحة
$paymentMethodsQuery = "SELECT DISTINCT method_name FROM wallet_addresses WHERE status = 'active' ORDER BY sort_order ASC";
$paymentMethodsResult = mysqli_query($conn, $paymentMethodsQuery);
$paymentMethods = [];
while ($row = mysqli_fetch_assoc($paymentMethodsResult)) {
    $paymentMethods[] = $row['method_name'];
}

// جلب رموز العملات من جدول العملات
$currenciesQuery = "SELECT symbol FROM currencies ORDER BY id ASC";
$currenciesResult = mysqli_query($conn, $currenciesQuery);
$currencies = [];
while ($row = mysqli_fetch_assoc($currenciesResult)) {
    $currencies[] = $row['symbol'];
}

// إذا لم يتم العثور على عملات في الجدول، استخدم القيم الافتراضية
if (empty($currencies)) {
    $currencies = ["USD", "EGP", "JOD", "EUR"];
}

// جلب أنواع المشاريع
$projectTypesQuery = "SELECT DISTINCT project_type FROM projects";
$projectTypesResult = mysqli_query($conn, $projectTypesQuery);
$projectTypes = [];
while ($row = mysqli_fetch_assoc($projectTypesResult)) {
    $projectTypes[] = $row['project_type'];
}

// إضافة خيار المشاريع العاجلة
$urgentProjectsExist = false;
$urgentCheckQuery = "SELECT COUNT(*) as count FROM projects WHERE is_urgent = 'yes'";
$urgentCheckResult = mysqli_query($conn, $urgentCheckQuery);
if ($urgentCheckResult && $row = mysqli_fetch_assoc($urgentCheckResult)) {
    if ($row['count'] > 0) {
        $urgentProjectsExist = true;
    }
}
?>



<!-- إجمالي التبرعات حسب العملة -->
<div class="w-full mb-8">
    <?php
    $query = "SELECT currency, SUM(amount) AS total_amount FROM donations GROUP BY currency";
    $result = mysqli_query($conn, $query);

    // دالة لاختصار الأرقام
    function formatNumber($number)
    {
        if ($number >= 1000000) {
            return round($number / 1000000, 1) . 'M';
        } elseif ($number >= 1000) {
            return round($number / 1000, 1) . 'K';
        }
        return number_format($number, 2);
    }

    // جمع البيانات في مصفوفة
    $currencyData = [];
    while ($row = mysqli_fetch_assoc($result)) {
        if (in_array($row['currency'], $currencies)) {
            $currencyData[] = $row;
        }
    }

    // تقسيم البيانات إلى صفوف (كل صف يحتوي على 3 عملات كحد أقصى)
    $chunkedData = array_chunk($currencyData, 3);

    // عرض البيانات في صفوف
    foreach ($chunkedData as $rowData) {
        echo '<div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">';

        foreach ($rowData as $item) {
            $formattedAmount = formatNumber($item['total_amount']);
            $fullAmount = number_format($item['total_amount'], 2);

            echo "
            <div class='bg-white shadow-md hover:shadow-xl transition duration-300 ease-in-out p-6 rounded-lg flex items-center space-x-4'>
                <div class='text-primary'>
                    <i class='text-6xl md:text-5xl text-green-500 drop-shadow-md'>{$item['currency']}</i>
                </div>
                <div class='flex-1 mr-4'>
                    <div class='text-lg font-medium text-gray-600 rtl'>إجمالي مبالغ التبرعات - {$item['currency']}</div>
                    <div class='text-2xl md:text-3xl text-gray-800 rtl font-bold' title='{$fullAmount}'>
                        {$formattedAmount}
                    </div>
                </div>
            </div>
            ";
        }

        echo '</div>';
    }
    ?>
</div>

<div class="container mx-auto p-4">
    <!-- فلاتر التبرعات -->
    <div class="bg-white p-4 rounded-lg shadow-md mb-6 rtl">
        <h3 class="text-lg font-semibold text-gray-700 mb-4">تصفية التبرعات</h3>
        <form id="filter-form" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- فلتر العملة -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">العملة</span>
                </label>
                <select id="currency-filter" class="select select-bordered w-full">
                    <option value="">جميع العملات</option>
                    <?php foreach ($currencies as $currency): ?>
                        <option value="<?php echo $currency; ?>"><?php echo $currency; ?></option>
                    <?php endforeach; ?>
                </select>
            </div>

            <!-- فلتر نوع المشروع -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">نوع المشروع</span>
                </label>
                <select id="project-type-filter" class="select select-bordered w-full">
                    <option value="">جميع أنواع المشاريع</option>
                    <?php foreach ($projectTypes as $type): ?>
                        <option value="<?php echo $type; ?>">
                            <?php echo $type == 'always' ? 'مشروع دائم' : ($type == 'renewed' ? 'مشروع متجدد' : $type); ?>
                        </option>
                    <?php endforeach; ?>
                    <?php if ($urgentProjectsExist): ?>
                        <option value="urgent">مشروع عاجل</option>
                    <?php endif; ?>
                </select>
            </div>

            <!-- فلتر طريقة الدفع -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">طريقة الدفع</span>
                </label>
                <select id="payment-method-filter" class="select select-bordered w-full">
                    <option value="">جميع طرق الدفع</option>
                    <?php foreach ($paymentMethods as $method): ?>
                        <option value="<?php echo $method; ?>"><?php echo $method; ?></option>
                    <?php endforeach; ?>
                </select>
            </div>

            <!-- فلتر نطاق التاريخ -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">نطاق التاريخ</span>
                </label>
                <div class="flex gap-2">
                    <input type="date" id="date-from" class="input input-bordered w-full" placeholder="من">
                    <input type="date" id="date-to" class="input input-bordered w-full" placeholder="إلى">
                </div>
            </div>

            <!-- أزرار التصفية -->
            <div class="form-control md:col-span-2 lg:col-span-4 mt-2 w-full">
                <div class="grid grid-cols-4 gap-2">
                    <button type="button" id="apply-filters" class="btn btn-primary bg-green-500 hover:bg-green-600 text-white border-none col-span-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                        </svg>
                        تطبيق الفلاتر
                    </button>
                    <button type="button" id="export-excel" class="btn btn-success text-white border-none">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        تصدير إلى Excel
                    </button>
                    <button type="button" id="reset-filters" class="btn btn-outline col-span-1">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        إعادة تعيين
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- حاوية التبرعات -->
    <div id="donations-container" class="mb-8">
        <!-- سيتم تحميل الجدول هنا عبر AJAX -->
        <div class="flex justify-center items-center h-64">
            <span class="loading loading-spinner loading-lg text-green-500"></span>
        </div>
    </div>
</div>

<script>
    // دالة لجلب البيانات مع الفلاتر
    function loadDonations(page = 1, perPage = 15, filters = {}) {
        // عرض مؤشر التحميل
        document.getElementById('donations-container').innerHTML = `
            <div class="flex justify-center items-center h-64">
                <span class="loading loading-spinner loading-lg text-green-500"></span>
            </div>`;

        // بناء عنوان URL مع المعلمات
        let url = `/admin/sections/api/get_donations.php?page=${page}&per_page=${perPage}`;

        // إضافة معلمات الفلتر إلى URL
        if (filters.currency) url += `&currency=${filters.currency}`;
        if (filters.projectType) url += `&project_type=${filters.projectType}`;
        if (filters.paymentMethod) url += `&payment_method=${filters.paymentMethod}`;
        if (filters.dateFrom) url += `&date_from=${filters.dateFrom}`;
        if (filters.dateTo) url += `&date_to=${filters.dateTo}`;

        fetch(url)
            .then(response => response.json())
            .then(data => {
                document.getElementById('donations-container').innerHTML = data.data + (data.pagination || '');
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('donations-container').innerHTML = `
                    <div class="alert alert-error shadow-lg">
                        <div class="flex-1">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                            <label>حدث خطأ أثناء جلب البيانات</label>
                        </div>
                    </div>`;
            });
    }

    // دالة لتغيير الصفحة مع الحفاظ على الفلاتر
    function loadPage(page) {
        // جمع قيم الفلاتر الحالية
        const filters = getCurrentFilters();
        loadDonations(page, 15, filters);
    }

    // دالة للحصول على قيم الفلاتر الحالية
    function getCurrentFilters() {
        return {
            currency: document.getElementById('currency-filter').value,
            projectType: document.getElementById('project-type-filter').value,
            paymentMethod: document.getElementById('payment-method-filter').value,
            dateFrom: document.getElementById('date-from').value,
            dateTo: document.getElementById('date-to').value
        };
    }

    // دالة لتطبيق الفلاتر
    function applyFilters() {
        const filters = getCurrentFilters();
        loadDonations(1, 15, filters); // إعادة تحميل الصفحة الأولى مع الفلاتر
    }

    // دالة لإعادة تعيين الفلاتر
    function resetFilters() {
        // إعادة تعيين قيم الفلاتر
        document.getElementById('currency-filter').value = '';
        document.getElementById('project-type-filter').value = '';
        document.getElementById('payment-method-filter').value = '';
        document.getElementById('date-from').value = '';
        document.getElementById('date-to').value = '';

        // إعادة تحميل البيانات بدون فلاتر
        loadDonations(1, 15, {});
    }

    // دالة لتصدير البيانات إلى Excel
    function exportToExcel() {
        // عرض مؤشر التحميل
        Swal.fire({
            title: 'جاري تصدير البيانات...',
            text: 'يرجى الانتظار قليلاً',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // جمع قيم الفلاتر الحالية
        const filters = getCurrentFilters();

        // بناء عنوان URL مع المعلمات
        let url = `/admin/sections/api/export_donations.php?format=excel`;

        // إضافة معلمات الفلتر إلى URL
        if (filters.currency) url += `&currency=${filters.currency}`;
        if (filters.projectType) url += `&project_type=${filters.projectType}`;
        if (filters.paymentMethod) url += `&payment_method=${filters.paymentMethod}`;
        if (filters.dateFrom) url += `&date_from=${filters.dateFrom}`;
        if (filters.dateTo) url += `&date_to=${filters.dateTo}`;

        // إنشاء عنصر iframe مخفي لتنزيل الملف
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        document.body.appendChild(iframe);

        // تعيين معالج الحدث لإغلاق مؤشر التحميل بعد اكتمال التحميل
        iframe.onload = function() {
            document.body.removeChild(iframe);
            Swal.close();
        };

        // تعيين المصدر للـ iframe لبدء التنزيل
        iframe.src = url;

        // إغلاق مؤشر التحميل بعد 3 ثوانٍ في حالة عدم اكتمال التحميل
        setTimeout(() => {
            Swal.close();
        }, 3000);
    }

    // تحميل البيانات وإضافة مستمعي الأحداث عند فتح الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        <?php if (!$vodafoneCashEnabled): ?>
        // تشغيل تنظيف تلقائي لتبرعات Vodafone Cash عند تحميل الصفحة
        fetch('/admin/sections/api/cleanup_vodafone_donations.php', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            console.log('تنظيف تلقائي:', data.message);
            // تحميل البيانات بعد التنظيف
            loadDonations();
        })
        .catch(error => {
            console.error('خطأ في التنظيف التلقائي:', error);
            // تحميل البيانات حتى لو فشل التنظيف
            loadDonations();
        });
        <?php else: ?>
        // تحميل البيانات الأولية
        loadDonations();
        <?php endif; ?>

        // إضافة مستمعي الأحداث للأزرار
        document.getElementById('apply-filters').addEventListener('click', applyFilters);
        document.getElementById('reset-filters').addEventListener('click', resetFilters);
        document.getElementById('export-excel').addEventListener('click', exportToExcel);

        // منع إرسال النموذج عند الضغط على Enter
        document.getElementById('filter-form').addEventListener('submit', function(e) {
            e.preventDefault();
            applyFilters();
        });
    });

    // دالة عرض التفاصيل
function showDetails(details) {
    // تحويل تنسيق التاريخ إذا كان موجوداً
    if (details['التاريخ']) {
        try {
            const dateTime = new Date(details['التاريخ']);
            const datePart = dateTime.toISOString().split('T')[0];
            let hour = dateTime.getHours();
            const minute = dateTime.getMinutes().toString().padStart(2, '0');
            const period = hour < 12 ? 'ص' : 'م';

            // تحويل إلى 12 ساعة
            hour = hour % 12 || 12;

            details['التاريخ'] = `${datePart} ${hour}:${minute} ${period}`;
        } catch (e) {
            console.error('Error formatting date:', e);
        }
    }

    const fieldGroups = [
        ['اسم المشروع', 'الأسم', 'البلد', 'واتساب', 'الايميل', 'طريقة الدفع', 'التاريخ'],
        ['نوع الكفاله', 'تكرار الكفاله', 'قيمة التبرع', 'العملة', 'عدد الأسهم', 'غرض التبرع']
    ];

    const content = `
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-right">
        ${fieldGroups.map(group => `
            <div class="space-y-3">
                ${group.map(key => `
                    <div class="bg-base-200 p-3 rounded-lg">
                        <div class="text-sm text-green-500">${key}</div>
                        <div class="text-base mt-1 text-base-content break-words">${details[key] || '---'}</div>
                    </div>
                `).join('')}
            </div>
        `).join('')}
        ${details['إثبات الدفع'] ? `
        <div class="mt-4 col-span-full border-t border-gray-300 pt-4">
            <div class="bg-base-200 p-3 rounded-lg">
                <div class="text-sm text-green-500 mb-2">إثبات الدفع</div>
                <div class="flex justify-center">${details['إثبات الدفع']}</div>
            </div>
        </div>` : ''}
    </div>`;

    Swal.fire({
        title: 'تفاصيل التبرع',
        html: content,
        width: '90%',
        confirmButtonText: 'إغلاق',
        customClass: {
            popup: 'rtl bg-base-100 text-base-content max-w-5xl border border-gray-300 shadow-lg',
            title: 'text-2xl text-green-500 font-bold mb-4 border-b border-gray-300 pb-4',
            confirmButton: 'btn btn-soft btn-error'
        },
        showCloseButton: true,
        showClass: {
            popup: 'animate__animated animate__fadeIn'
        },
        didOpen: () => {
            document.querySelectorAll('.proof-image').forEach(img => {
                img.closest('a').addEventListener('click', e => {
                    e.preventDefault();
                    window.open(img.src, '_blank');
                });
            });
        }
    });
}

    // دالة تأكيد الحذف
    function confirmDelete(donationId) {
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "لن تتمكن من استعادة هذا التبرع!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'نعم، احذفه!',
            cancelButtonText: 'إلغاء',
            customClass: {
                popup: 'rtl',
                confirmButton: 'btn btn-error',
                cancelButton: 'btn btn-ghost'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                deleteDonation(donationId);
            }
        });
    }

    // دالة تنفيذ الحذف عبر AJAX
    function deleteDonation(id) {

        // الحصول على رقم الصفحة الحالية من عناصر التقسيم
        const currentPage = document.querySelector('.join .btn-active')?.textContent || 1;


        // استخدام FormData لإرسال البيانات
        const formData = new FormData();
        formData.append('_method', 'DELETE');
        formData.append('id', id);

        fetch('/admin/sections/api/delete_donation.php?id=' + id, {
                method: 'POST',
                headers: {
                    'X-HTTP-Method-Override': 'DELETE'
                },
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // إزالة الصف من الجدول
                    const row = document.getElementById('row-' + id);
                    if (row) {
                        row.style.transition = 'opacity 0.5s ease';
                        row.style.opacity = '0';
                        setTimeout(() => row.remove(), 500);
                    }

                    Swal.fire({
                        title: 'تم الحذف!',
                        text: 'تم حذف التبرع بنجاح.',
                        icon: 'success',
                        confirmButtonText: 'حسناً'
                    }).then(() => {
                        // استخدام currentPage بدلاً من page
                        loadPage(currentPage);
                    });
                } else {
                    Swal.fire({
                        title: 'خطأ!',
                        text: 'حدث خطأ أثناء محاولة الحذف: ' + (data.message || ''),
                        icon: 'error',
                        confirmButtonText: 'حسناً'
                    });
                }
            })
            .catch(error => {
                Swal.fire({
                    title: 'خطأ!',
                    text: 'حدث خطأ في الاتصال بالخادم: ' + error.message,
                    icon: 'error',
                    confirmButtonText: 'حسناً'
                });
            });
    }
</script>