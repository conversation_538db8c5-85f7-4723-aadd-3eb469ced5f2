<?php
// إعدادات Vodafone Cash
// تغيير إلى false لتعطيل تبرعات Vodafone Cash الجديدة
$vodafoneCashEnabled = false;

// نظام تحديد تاريخ التعطيل تلقائياً
$statusFile = __DIR__ . '/vodafone_status.json';

// إذا كان الملف غير موجود، إنشاؤه
if (!file_exists($statusFile)) {
    $statusData = [
        'last_status' => true,
        'disabled_date' => null
    ];
    file_put_contents($statusFile, json_encode($statusData));
}

// قراءة الحالة السابقة
$statusData = json_decode(file_get_contents($statusFile), true);

// إذا تم تغيير الحالة من true إلى false، حفظ تاريخ اليوم
if ($statusData['last_status'] === true && $vodafoneCashEnabled === false) {
    $statusData['disabled_date'] = date('Y-m-d H:i:s');
    $statusData['last_status'] = false;
    file_put_contents($statusFile, json_encode($statusData));
} elseif ($vodafoneCashEnabled === true) {
    $statusData['last_status'] = true;
    file_put_contents($statusFile, json_encode($statusData));
}

// تحديد تاريخ التعطيل
$vodafoneCashDisabledDate = $vodafoneCashEnabled === false && $statusData['disabled_date']
    ? date('Y-m-d', strtotime($statusData['disabled_date']))
    : date('Y-m-d'); // إذا لم يكن هناك تاريخ محفوظ، استخدم تاريخ اليوم
?>
