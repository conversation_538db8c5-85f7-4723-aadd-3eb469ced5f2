<?php
require '../config/conn.php';

$query = "SELECT * FROM settings LIMIT 1";
$result = mysqli_query($conn, $query);

if ($result && mysqli_num_rows($result) > 0) {
    $settings = mysqli_fetch_assoc($result);
} else {
    $settings = [
        'whatsapp' => '',
        'email' => '',
        'facebook_url' => '',
        'twitter_url' => '',
        'telegram_url' => '',
        'instagram_url' => '',
        'tiktok_url' => '',
        'youtube_url' => '',
        'linkedin_url' => '',
        'beneficiary_family' => '',
        'vodafone_cash_enabled' => '1',
    ];
}
?>

<div class="bg-gray-100 flex justify-center items-center min-h-screen">
    <div class="bg-white shadow-lg rounded-lg p-6 w-full max-w-lg mx-4 max-w-6xl rtl">
        <h1 class="text-3xl font-semibold text-center mb-6 text-gray-800"> الإعدادات</h1>

        <!-- نموذج تعديل البيانات -->
        <form class="space-y-4" id="settingsForm">
            <div class="form-control w-full">
                <label for="whatsapp" class="label">
                    <span class="label-text">رقم الواتساب:</span>
                </label>
                <input type="text" id="whatsapp" name="whatsapp" value="<?php echo $settings['whatsapp']; ?>" class="input input-bordered w-full">
            </div>

            <div class="form-control w-full">
                <label for="beneficiary_family" class="label">
                    <span class="label-text">عدد الاشخاص المستفيدين:</span>
                </label>
                <input type="number" id="beneficiary_family" name="beneficiary_family" value="<?php echo $settings['beneficiary_family']; ?>" class="input input-bordered w-full">
            </div>


            <div class="form-control w-full">
                <label for="email" class="label">
                    <span class="label-text">البريد الإلكتروني:</span>
                </label>
                <input type="email" id="email" name="email" value="<?php echo $settings['email']; ?>" class="input input-bordered w-full">
            </div>

            <div class="form-control w-full">
                <label for="facebook_url" class="label">
                    <span class="label-text">فيسبوك:</span>
                </label>
                <input type="url" id="facebook_url" name="facebook_url" value="<?php echo $settings['facebook_url']; ?>" class="input input-bordered w-full">
            </div>

            <div class="form-control w-full">
                <label for="twitter_url" class="label">
                    <span class="label-text">تويتر:</span>
                </label>
                <input type="url" id="twitter_url" name="twitter_url" value="<?php echo $settings['twitter_url']; ?>" class="input input-bordered w-full">
            </div>

            <div class="form-control w-full">
                <label for="telegram_url" class="label">
                    <span class="label-text">تليجرام:</span>
                </label>
                <input type="url" id="telegram_url" name="telegram_url" value="<?php echo $settings['telegram_url']; ?>" class="input input-bordered w-full">
            </div>

            <div class="form-control w-full">
                <label for="instagram_url" class="label">
                    <span class="label-text">إنستجرام:</span>
                </label>
                <input type="url" id="instagram_url" name="instagram_url" value="<?php echo $settings['instagram_url']; ?>" class="input input-bordered w-full">
            </div>

            <div class="form-control w-full">
                <label for="tiktok_url" class="label">
                    <span class="label-text">تيكتوك:</span>
                </label>
                <input type="url" id="tiktok_url" name="tiktok_url" value="<?php echo $settings['tiktok_url']; ?>" class="input input-bordered w-full">
            </div>

            <div class="form-control w-full">
                <label for="youtube_url" class="label">
                    <span class="label-text">يوتيوب:</span>
                </label>
                <input type="url" id="youtube_url" name="youtube_url" value="<?php echo $settings['youtube_url']; ?>" class="input input-bordered w-full">
            </div>

            <div class="form-control w-full">
                <label for="linkedin_url" class="label">
                    <span class="label-text">لينكدإن:</span>
                </label>
                <input type="url" id="linkedin_url" name="linkedin_url" value="<?php echo $settings['linkedin_url']; ?>" class="input input-bordered w-full">
            </div>


            <!-- زر التحديث -->
            <div class="flex justify-center">
                <button type="button" id="updateSettings" class="btn btn-primary mt-4 w-full">تحديث الإعدادات</button>
            </div>
        </form>
    </div>
</div>

<script>
    const originalValues = <?php echo json_encode([
                                'whatsapp' => $settings['whatsapp'],
                                'email' => $settings['email'],
                                'facebook_url' => $settings['facebook_url'],
                                'twitter_url' => $settings['twitter_url'],
                                'telegram_url' => $settings['telegram_url'],
                                'instagram_url' => $settings['instagram_url'],
                                'tiktok_url' => $settings['tiktok_url'],
                                'youtube_url' => $settings['youtube_url'],
                                'linkedin_url' => $settings['linkedin_url'],
                                'beneficiary_family' => $settings['beneficiary_family']
                            ]); ?>;

    document.getElementById('updateSettings').addEventListener('click', function() {
        const inputs = document.querySelectorAll('#settingsForm input');

        inputs.forEach(input => {
            const field = input.name;
            const value = input.value;

            if (value !== originalValues[field]) {
                fetch('/admin/sections/api/update_settings.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        },
                        body: `field=${field}&value=${encodeURIComponent(value)}`
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === "success") {
                            alert(data.message);
                        } else {
                            alert(data.message);
                        }
                    })
                    .catch(error => console.error("Error connecting:", error));
            }
        });
    });
</script>