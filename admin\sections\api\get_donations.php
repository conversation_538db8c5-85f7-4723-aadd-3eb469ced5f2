<?php
require '../../../config/conn.php';

header('Content-Type: application/json');

// متغير للتحكم في تعطيل تبرعات Vodafone Cash
// يجب أن يكون نفس القيمة في donations.php
$vodafoneCashEnabled = true;

function renderRow($value, $class = '')
{
    return '<td class="px-6 py-4 ' . $class . '">' . htmlspecialchars($value) . '</td>';
}

// معلمات التقسيم
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 10;
$offset = ($page - 1) * $perPage;

// معلمات الفلتر
$currency = isset($_GET['currency']) ? $_GET['currency'] : '';
$projectType = isset($_GET['project_type']) ? $_GET['project_type'] : '';
$paymentMethod = isset($_GET['payment_method']) ? $_GET['payment_method'] : '';
$dateFrom = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$dateTo = isset($_GET['date_to']) ? $_GET['date_to'] : '';

// بناء شرط WHERE للفلاتر
$whereConditions = [];
$params = [];
$types = '';

if (!empty($currency)) {
    $whereConditions[] = "currency = ?";
    $params[] = $currency;
    $types .= 's';
}

if (!empty($projectType)) {
    if ($projectType === 'urgent') {
        // الحصول على معرفات المشاريع العاجلة
        $projectIdsQuery = "SELECT id FROM projects WHERE is_urgent = 'yes'";
        $stmt = $conn->prepare($projectIdsQuery);
    } else {
        // الحصول على معرفات المشاريع من النوع المحدد
        $projectIdsQuery = "SELECT id FROM projects WHERE project_type = ?";
        $stmt = $conn->prepare($projectIdsQuery);
        $stmt->bind_param('s', $projectType);
    }

    $stmt->execute();
    $result = $stmt->get_result();

    $projectIds = [];
    while ($row = $result->fetch_assoc()) {
        $projectIds[] = $row['id'];
    }
    $stmt->close();

    if (!empty($projectIds)) {
        $projectIdsStr = implode(',', $projectIds);
        $whereConditions[] = "project_id IN ($projectIdsStr)";
    } else {
        // إذا لم يتم العثور على مشاريع من هذا النوع، نضيف شرطًا مستحيلًا لإرجاع نتيجة فارغة
        $whereConditions[] = "1 = 0";
    }
}

if (!empty($paymentMethod)) {
    $whereConditions[] = "payment_method = ?";
    $params[] = $paymentMethod;
    $types .= 's';
}

if (!empty($dateFrom)) {
    $whereConditions[] = "DATE(donation_date) >= ?";
    $params[] = $dateFrom;
    $types .= 's';
}

if (!empty($dateTo)) {
    $whereConditions[] = "DATE(donation_date) <= ?";
    $params[] = $dateTo;
    $types .= 's';
}

// إخفاء تبرعات Vodafone Cash الجديدة إذا كانت معطلة
if (!$vodafoneCashEnabled) {
    // الحصول على تاريخ آخر تعطيل للخاصية (افتراضياً اليوم)
    $disableDate = date('Y-m-d');
    $whereConditions[] = "NOT (LOWER(payment_method) = 'vodafone cash' AND DATE(donation_date) >= ?)";
    $params[] = $disableDate;
    $types .= 's';
}

// بناء استعلام العدد الكلي مع الفلاتر
$totalQuery = "SELECT COUNT(*) as total FROM donations";
if (!empty($whereConditions)) {
    $totalQuery .= " WHERE " . implode(' AND ', $whereConditions);
}

// تنفيذ استعلام العدد الكلي
if (!empty($params)) {
    $stmt = $conn->prepare($totalQuery);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $totalResult = $stmt->get_result();
    $totalRows = $totalResult->fetch_assoc()['total'];
    $stmt->close();
} else {
    $totalResult = $conn->query($totalQuery);
    $totalRows = $totalResult->fetch_assoc()['total'];
}

$totalPages = ceil($totalRows / $perPage);

// بناء استعلام جلب البيانات مع الفلاتر
$sql = "SELECT * FROM donations";
if (!empty($whereConditions)) {
    $sql .= " WHERE " . implode(' AND ', $whereConditions);
}
$sql .= " ORDER BY id DESC LIMIT $offset, $perPage";

// تنفيذ استعلام جلب البيانات
if (!empty($params)) {
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
} else {
    $result = $conn->query($sql);
}

$response = [
    'data' => '',
    'pagination' => ''
];

if ($result && $result->num_rows > 0) {
    $headers = ['#', 'اسم المشروع', 'الأسم', 'البلد', 'واتساب', 'طريقة الدفع', 'العملة', 'قيمة التبرع', 'التاريخ', 'معاينة', 'حذف'];

    // إنشاء رؤوس الجدول أولاً
    $tableHeaders = implode('', array_map(function ($h) {
        return "<th class='px-6 py-3 font-bold'>$h</th>";
    }, $headers));

    $tableContent = <<<HTML
    <div class="rounded-lg shadow-lg border border-base-200 bg-base-100 overflow-x-auto">
        <table class="table table-zebra table-auto w-full rtl">
            <thead class="bg-green-500 text-base-300 text-right">
                <tr>$tableHeaders</tr>
            </thead>
            <tbody class="text-right">
    HTML;

    while ($row = $result->fetch_assoc()) {
        $proofImages = explode(',', $row['proof_image']); // تقسيم الصور إلى مصفوفة
        $proofContent = '<div class="flex gap-2">'; // استخدام flex لعرض الملفات بجانب بعض

        foreach ($proofImages as $proofFile) {
            $proofFile = trim($proofFile); // إزالة الفراغات
            $fileExtension = strtolower(pathinfo($proofFile, PATHINFO_EXTENSION));
            $isPDF = ($fileExtension === 'pdf');

            if ($isPDF) {
                $proofContent .= "<a href='../../assets/img/proof/$proofFile' target='_blank' class='btn btn-error btn-sm gap-2'>
            PDF <svg xmlns='http://www.w3.org/2000/svg' class='h-4 w-4' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
                <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14'/>
            </svg>
        </a>";
            } else {
                $proofContent .= "<a href='../../assets/img/proof/$proofFile' target='_blank' class='cursor-zoom-in'>
            <img src='../../assets/img/proof/$proofFile' class='w-20 h-20 object-cover rounded-lg border-2 border-base-200' alt='إثبات الدفع'>
        </a>";
            }
        }

        $proofContent .= '</div>'; // إغلاق الـ div بعد عرض جميع الملفات


        $kafelType = 'ليس كفالة يتيم';
        if ($row['kafel_type']) {
            $kafelType = match ($row['kafel_type']) {
                'child' => 'طفل',
                'family' => 'أسرة',
                default => $row['kafel_type']
            };
        }

        $kafelFrequency = 'ليس كفالة يتيم';
        if ($row['kafel_frequency']) {
            $kafelFrequency = match ($row['kafel_frequency']) {
                'one-time' => 'لمرة واحدة',
                'monthly' => 'شهري',
                default => $row['kafel_frequency']
            };
        }

        $details = [
            'اسم المشروع' => htmlspecialchars($row['project_title']),
            'الأسم' => htmlspecialchars($row['name']),
            'البلد' => htmlspecialchars($row['country']),
            'واتساب' => htmlspecialchars($row['whatsapp']),
            'الايميل' => htmlspecialchars($row['email']),
            'نوع الكفاله' => $kafelType,
            'تكرار الكفاله' => $kafelFrequency,
            'طريقة الدفع' => htmlspecialchars($row['payment_method']),
            'قيمة التبرع' => htmlspecialchars($row['amount']),
            'العملة' => htmlspecialchars($row['currency']),
            'عدد الأسهم' => htmlspecialchars($row['number_shares']),
            'غرض التبرع' => htmlspecialchars($row['purpose']),
            'التاريخ' => htmlspecialchars($row['donation_date']),
            'إثبات الدفع' => $proofContent
        ];

        // تحويل تنسيق التاريخ والوقت ليشمل الدقائق
        $dateTime = new DateTime($row['donation_date']);
        $datePart = $dateTime->format('Y-m-d');
        $hour = (int)$dateTime->format('H');
        $minute = $dateTime->format('i'); // إضافة الدقائق
        // تحديد ص/م
        $period = ($hour < 12) ? 'ص' : 'م';
        // تحويل إلى 12 ساعة
        $hour12 = ($hour > 12) ? $hour - 12 : $hour;
        $hour12 = ($hour12 == 0) ? 12 : $hour12; // الساعة 00 تصبح 12
        $formattedDate = $datePart . ' ' . $hour12 . ':' . $minute . ' ' . $period;


        $tableContent .= '<tr class="hover">' .
            // هنا تم التعديل لإضافة لون أخضر لرقم ID
            '<td class="px-6 py-4 text-green-600 font-bold">' . htmlspecialchars($row['id']) . '</td>' .
            renderRow($row['project_title']) .
            renderRow($row['name']) .
            renderRow($row['country']) .
            renderRow($row['whatsapp']) .
            renderRow($row['payment_method']) .
            renderRow($row['currency']) .
            renderRow($row['amount'], 'font-bold') .
            '<td class="px-6 py-4">' . htmlspecialchars($formattedDate) . '</td>' .
            '<td class="px-6 py-4">' .
            '<button onclick="showDetails(' . htmlspecialchars(json_encode($details), ENT_QUOTES, 'UTF-8') . ')" ' .
            'class="bg-green-500 text-base-300 py-2 px-4 flex flex-row items-center gap-2 flex-nowrap rounded">' .
            '<span>التفاصيل</span>' .
            '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">' .
            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>' .
            '</svg>' .
            '</button>' .
            '</td>' .
            // زر الحذف الجديد
            '<td class="px-6 py-4">' .
            '<button onclick="confirmDelete(' . htmlspecialchars($row['id']) . ')" ' .
            'class="btn btn-error btn-sm text-white">' .
            '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">' .
            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>' .
            '</svg>' .
            '</button>' .
            '</td>' .


            '</tr>';
    }

    $tableContent .= '</tbody></table></div>';

    // إنشاء واجهة التقسيم المحسنة
    $pagination = '<div class="flex justify-center mt-6">';
    $pagination .= '<div class="join">';

    // إضافة معلمات الفلتر إلى عنوان URL للتقسيم
    $filterParams = '';
    if (!empty($currency)) $filterParams .= '&currency=' . urlencode($currency);
    if (!empty($projectType)) $filterParams .= '&project_type=' . urlencode($projectType);
    if (!empty($paymentMethod)) $filterParams .= '&payment_method=' . urlencode($paymentMethod);
    if (!empty($dateFrom)) $filterParams .= '&date_from=' . urlencode($dateFrom);
    if (!empty($dateTo)) $filterParams .= '&date_to=' . urlencode($dateTo);

    // زر السابق مع تعطيل إذا كانت الصفحة الأولى
    if ($page <= 1) {
        $pagination .= '
        <button class="join-item btn btn-disabled" disabled>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
            التالي
        </button>';
    } else {
        $pagination .= '
        <button class="join-item btn" onclick="loadPage(' . ($page - 1) . ')">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
            التالي
        </button>';
    }

    // أرقام الصفحات
    $startPage = max(1, $page - 2);
    $endPage = min($totalPages, $page + 2);

    if ($startPage > 1) {
        $pagination .= '<button class="join-item btn" onclick="loadPage(1)">1</button>';
        if ($startPage > 2) {
            $pagination .= '<button class="join-item btn btn-disabled" disabled>...</button>';
        }
    }

    for ($i = $startPage; $i <= $endPage; $i++) {
        $active = ($i == $page) ? 'btn-active' : '';
        $pagination .= '<button class="join-item btn ' . $active . '" onclick="loadPage(' . $i . ')">' . $i . '</button>';
    }

    if ($endPage < $totalPages) {
        if ($endPage < $totalPages - 1) {
            $pagination .= '<button class="join-item btn btn-disabled" disabled>...</button>';
        }
        $pagination .= '<button class="join-item btn" onclick="loadPage(' . $totalPages . ')">' . $totalPages . '</button>';
    }

    // زر التالي مع تعطيل إذا كانت الصفحة الأخيرة
    if ($page >= $totalPages) {
        $pagination .= '
        <button class="join-item btn btn-disabled" disabled>
            السابق
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
        </button>';
    } else {
        $pagination .= '
        <button class="join-item btn" onclick="loadPage(' . ($page + 1) . ')">
        السابق
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
        </button>';
    }

    $pagination .= '</div></div>';

    $response['data'] = $tableContent;
    $response['pagination'] = $pagination;
} else {
    $response['data'] = '<div class="alert alert-info shadow-lg">
            <div class="flex-1">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="w-6 h-6 stroke-current">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <label>لا توجد تبرعات مسجلة</label>
            </div>
          </div>';
}

echo json_encode($response);
$conn->close();
