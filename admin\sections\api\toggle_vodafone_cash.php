<?php
require '../../../config/conn.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (isset($input['enabled'])) {
        $enabled = $input['enabled'] ? 1 : 0;
        
        // حفظ الحالة في ملف مؤقت أو session
        $configFile = '../../../config/vodafone_cash_config.json';
        $config = ['enabled' => $enabled];
        
        if (file_put_contents($configFile, json_encode($config))) {
            // إذا تم تعطيل Vodafone Cash، احذف التبرعات الجديدة
            if (!$enabled) {
                // تحديد التاريخ الحالي كنقطة فاصلة
                $currentDateTime = date('Y-m-d H:i:s');
                
                // حذف التبرعات الجديدة بـ Vodafone Cash (التي تم إنشاؤها بعد تعطيل الخاصية)
                $deleteQuery = "DELETE FROM donations 
                               WHERE LOWER(payment_method) = 'vodafone cash' 
                               AND donation_date >= ?";
                
                $stmt = $conn->prepare($deleteQuery);
                $stmt->bind_param('s', $currentDateTime);
                
                if ($stmt->execute()) {
                    echo json_encode([
                        'success' => true, 
                        'message' => 'تم تعطيل Vodafone Cash وحذف التبرعات الجديدة'
                    ]);
                } else {
                    echo json_encode([
                        'success' => true, 
                        'message' => 'تم تعطيل Vodafone Cash'
                    ]);
                }
            } else {
                echo json_encode([
                    'success' => true, 
                    'message' => 'تم تفعيل Vodafone Cash'
                ]);
            }
        } else {
            echo json_encode([
                'success' => false, 
                'message' => 'فشل في حفظ الإعدادات'
            ]);
        }
    } else {
        echo json_encode([
            'success' => false, 
            'message' => 'بيانات غير صحيحة'
        ]);
    }
} else {
    echo json_encode([
        'success' => false, 
        'message' => 'طريقة الطلب غير مسموحة'
    ]);
}

$conn->close();
?>
