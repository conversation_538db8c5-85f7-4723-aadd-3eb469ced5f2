<?php
require '../../../config/conn.php';

// قراءة إعدادات Vodafone Cash من الملف المشترك
require '../../../config/vodafone_cash_config.php';

// إذا كان Vodafone Cash معطل، احذف التبرعات الجديدة
if (!$vodafoneCashEnabled) {
    // استخدام تاريخ تفعيل التعطيل كنقطة فاصلة
    $disableDate = $vodafoneCashDisabledDate;

    // جلب التبرعات الجديدة بـ Vodafone Cash مع أسماء الملفات (بعد تاريخ التعطيل)
    $selectQuery = "SELECT id, proof_image FROM donations
                   WHERE LOWER(payment_method) = 'vodafone cash'
                   AND DATE(donation_date) >= ?";

    $selectStmt = $conn->prepare($selectQuery);
    $selectStmt->bind_param('s', $disableDate);
    $selectStmt->execute();
    $result = $selectStmt->get_result();
    
    $deletedCount = 0;
    
    while ($row = $result->fetch_assoc()) {
        $donationId = $row['id'];
        $proofImages = $row['proof_image'];
        
        // حذف ملفات الإثبات المرتبطة
        if (!empty($proofImages)) {
            $imageFiles = explode(',', $proofImages);
            foreach ($imageFiles as $fileName) {
                $filePath = '../../../assets/img/proof/' . trim($fileName);
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }
        }
        
        // حذف التبرع من قاعدة البيانات
        $deleteStmt = $conn->prepare("DELETE FROM donations WHERE id = ?");
        $deleteStmt->bind_param('i', $donationId);
        if ($deleteStmt->execute()) {
            $deletedCount++;
        }
    }
    
    echo json_encode([
        'success' => true, 
        'message' => "تم حذف $deletedCount تبرع من Vodafone Cash",
        'deleted_count' => $deletedCount
    ]);
} else {
    echo json_encode([
        'success' => true, 
        'message' => 'Vodafone Cash مفعل - لا حاجة للحذف'
    ]);
}

$conn->close();
?>
