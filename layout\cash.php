<?php
require($_SERVER['DOCUMENT_ROOT'] . '/config/conn.php');


$queryWallets = "SELECT * FROM wallet_addresses WHERE status = 'active' ORDER BY sort_order ASC";
$resultWallets = mysqli_query($conn, $queryWallets);

$walletAddresses = [];
if ($resultWallets && mysqli_num_rows($resultWallets) > 0) {
    while ($row = mysqli_fetch_assoc($resultWallets)) {
        $addresses = json_decode($row['address'], true);
        if (json_last_error() === JSON_ERROR_NONE) {
            $walletAddresses[$row['method_name']] = [
                'addresses' => $addresses,
                'image' => $row['method_image'],
            ];
        }
    }
}

$queryProjects = "SELECT id, title, project_kafel FROM projects";
$resultProjects = mysqli_query($conn, $queryProjects);

$projects = [];
if ($resultProjects && mysqli_num_rows($resultProjects) > 0) {
    while ($row = mysqli_fetch_assoc($resultProjects)) {
        $projects[] = [
            'id' => $row['id'],
            'title' => $row['title'],
            'kafel' => $row['project_kafel']
        ];
    }
}

// جلب العملات
$currencies = getCurrencies();

// إنشاء مصفوفة لأسعار الأسهم لاستخدامها في JavaScript
$sharePrices = [];
foreach ($currencies as $currency) {
    $sharePrices[$currency['symbol']] = $currency['value'];
}

mysqli_close($conn);
?>

<style>
    label {
        margin: 15px 0 15px;
    }
</style>

<div class="select_payment my-10 rtl">
    <h2 class="text-3xl font-bold text-center pb-5">اختر الطريقة المناسبة</h2>
    <div class="border-b-2 w-1/2 mx-auto"></div>

    <div class="payment_method max-w-6xl mx-auto p-6 grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-5 gap-8">
        <?php foreach ($walletAddresses as $methodName => $data): ?>
            <div id="<?= strtolower($methodName) ?>" class="rounded-lg shadow-lg p-2 bg-white bg-opacity-40 transition hover:shadow-xl flex flex-col items-center gap-4 bg-base-300">
                <div class="body flex justify-center mt-4">
                    <div class="overlay w-40 h-40 bg-white bg-opacity-50 rounded-full flex items-center justify-center">
                        <img src="/assets/img/wallets/<?= $data['image'] ?>" alt="<?= $methodName ?> logo" class="w-24 h-24 object-contain">
                    </div>
                </div>
                <button onclick="handleDonation('<?= $methodName ?>')" class="m-1 cursor-pointer text-lg font-bold text-white" style="background-color: #c3875d; padding: 10px 20px; border-radius: 50px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                    تبرع
                </button>
            </div>
        <?php endforeach; ?>
    </div>
</div>

<script>
    function handleDonation(methodName) {
        Swal.fire({
            title: '<span style="color: #10b981;">إرفاق فاتورة الدفع</span>',
            width: '100%',
            html: `

            <!-- ملاحظة مهمة للمتبرعين -->
<div dir="rtl" class="bg-yellow-50 text-right border-r-4 border-yellow-500 p-4 rounded-lg shadow-sm mb-6">
  <div class="flex items-start">
    <div class="flex-shrink-0">
      <svg class="h-5 w-5 text-yellow-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856
                 c1.54 0 2.502-1.667 1.732-3L13.732 4
                 c-.77-1.333-2.694-1.333-3.464 0L3.34 16
                 c-.77 1.333.192 3 1.732 3z" />
      </svg>
    </div>
    <div class="mr-3">
      <h3 class="text-md font-bold text-yellow-800">ملاحظة مهمة</h3>
      <div class="mt-1 text-sm text-yellow-700">
        <p>يرجى التأكد من ملء جميع البيانات المطلوبة بشكل صحيح وبيانات حقيقية وإرفاق صورة فاتورة التبرع أو إيصال الدفع حتى يتم قبول تبرعكم.</p>
      </div>
    </div>
  </div>
</div>



        <form id="donationForm" enctype="multipart/form-data">
            <div class="mb-4">
                <label for="projectName" class="block text-right text-md font-medium text-gray-700">المشروع</label>
                <select id="projectName" class="mt-1 p-4 rtl block w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-opacity-50" required>
                    <option value="" disabled selected hidden>اختر المشروع</option>
                    <?php
                    foreach ($projects as $project) {
                        echo '<option value="' . htmlspecialchars($project['title']) . '" project-id="' . htmlspecialchars($project['id']) . '" data-kafel="' . htmlspecialchars($project['kafel']) . '">' . htmlspecialchars($project['title']) . '</option>';
                    }
                    ?>
                </select>
            </div>
            <div id="kafelFields"></div>
            <div class="mb-4">
                <label for="name" class="block text-right text-md font-medium text-gray-700">الاسم</label>
                <input type="text" id="name" class="mt-1 p-4 rtl block w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-opacity-50" placeholder="أدخل الاسم" required>
            </div>
            <div class="mb-4">
                <label for="country" class="block text-right text-md font-medium text-gray-700">البلد</label>
                <input type="text" id="country" class="mt-1 p-4 rtl block w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-opacity-50" placeholder="أدخل البلد" required>
            </div>
            <div class="mb-4">
                <label for="whatsapp" class="block text-right text-md font-medium text-gray-700">واتساب للتواصل</label>
                <input type="text" id="whatsapp" class="mt-1 p-4 rtl block w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-opacity-50" placeholder="أدخل رقم الواتساب" required>
            </div>
            <div class="mb-4">
                <label for="email" class="block text-right text-md font-medium text-gray-700">البريد الإلكتروني</label>
                <input type="email" id="email" class="mt-1 p-4 rtl block w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-opacity-50" placeholder="أدخل البريد الإلكتروني" required>
            </div>
            <div class="mb-4">
                <label for="currency" class="block text-right text-md font-medium text-gray-700">العملة</label>
                <select id="currency" class="mt-1 p-4 rtl block w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-opacity-50" required>
                    <option value="" disabled selected hidden>أختر العملة</option>
                    <?php foreach ($currencies as $currency): ?>
                        <option value="<?php echo $currency['symbol']; ?>"><?php echo $currency['name_ar']; ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="mb-4 relative">

<label for="donation_amount" class="block text-right text-md font-medium text-gray-700">قيمة التبرع</label>
<input type="number" id="donation_amount" class="mt-1 p-4 rtl block w-full border border-gray-300 rounded-md shadow-sm pl-16" placeholder="أدخل قيمة التبرع" min="0" required>
<input type="hidden" id="number_shares_value" name="number_shares_value">
<span id="number_shares" class="absolute bg-green-600 rtl text-white top-1/2 left-4 transform -translate-y-1/2 text-md px-2" style="top: calc(50% - 75px);">
0
</span>
<div class="Share_price py-3">
<?php foreach ($currencies as $currency): ?>
    <?php
        // تنسيق القيمة: إذا كانت القيمة رقماً صحيحاً، نعرضها بدون كسور عشرية
        $value = $currency['value'];
        $formattedValue = (floor($value) == $value) ? floor($value) : $value;

        // تحديد نص العملة المناسب
        $currencyText = '';
        switch ($currency['symbol']) {
            case 'USD':
                $currencyText = 'بالدولار';
                break;
            case 'EUR':
                $currencyText = 'بالـيورو';
                break;
            case 'EGP':
                $currencyText = 'بالجنيه المصري';
                break;
            case 'JOD':
                $currencyText = 'بالدينار الأردني';
                break;
            default:
            $currencyText = 'بالـ' . $currency['name_ar'];
        }
    ?>
    <p class="rtl text-green-600" data-currency="<?php echo $currency['symbol']; ?>" data-price="<?php echo $currency['value']; ?>">
        سعر السهم <?php echo $currencyText; ?> = <?php echo $formattedValue; ?> <?php echo $currency['name_ar']; ?>
    </p>
<?php endforeach; ?>
</div>

</div>
            <div class="mb-4">
                <label for="paymentMethod" class="block text-right text-md font-medium text-gray-700">وسيلة الدفع</label>
                <input type="text" onChange="handlePaymentMethodChange()" id="paymentMethod" class="mt-1 p-4 rtl block w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-opacity-50" value="${methodName}" disabled>
            </div>
            <div id="payment_method"></div>
            <div class="mb-4">
                <label for="purpose" class="block text-right text-md font-medium text-gray-700">غرض التبرع</label>
                <textarea id="purpose" class="mt-1 p-4 rtl block w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-opacity-50" placeholder="غرض التبرع" rows="3"></textarea>
            </div>
            <div class="mb-4">
            <label for="proofImage" class="block text-right text-md font-medium text-gray-700">
    يمكنك تحديد ورفع أكثر من صورة أو ملف PDF كمستند إثبات الدفع
</label>
                    <input type="file" id="proofImage" name="proofImages[]" multiple class="rtl file-input file-input-bordered mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-opacity-50" required>
                </div>
        </form>
        `,
            showCancelButton: true,
            confirmButtonText: 'إرسال',
            confirmButtonColor: '#10b981',
            cancelButtonText: 'إلغاء',
            didOpen: () => {
                updateKafelFields();
                handlePaymentMethodChange();
            },
            preConfirm: () => {
                const projectNameSelect = document.getElementById('projectName');
                const selectedOption = projectNameSelect.options[projectNameSelect.selectedIndex];
                const projectId = selectedOption ? selectedOption.getAttribute('project-id') : null;

                const projectName = document.getElementById('projectName').value;
                const name = document.getElementById('name').value;
                const country = document.getElementById('country').value;
                const whatsapp = document.getElementById('whatsapp').value;
                const email = document.getElementById('email').value;
                const currency = document.getElementById('currency').value;
                const paymentMethod = document.getElementById('paymentMethod').value;
                const donation_amount = document.getElementById('donation_amount').value;
                const number_shares = document.getElementById('number_shares').textContent;
                const purpose = document.getElementById('purpose').value;
                const kafelType = document.getElementById('kafelType') ? document.getElementById('kafelType').value : '';
                const kafelFrequency = document.getElementById('kafelFrequency') ? document.getElementById('kafelFrequency').value : '';


                var proofImageInput = document.getElementById('proofImage');
                const files = proofImageInput.files;

                if (files.length === 0) {
                    Swal.showValidationMessage('يرجى رفع صورة واحدة على الأقل');
                    return;
                }


                if (parseFloat(donation_amount) <= 0) {
                    Swal.showValidationMessage('مبلغ التبرع يجب أن يكون أكبر من الصفر');
                    return;
                }

                if (!projectName || !name || !country || !whatsapp || !email || !proofImageInput || !paymentMethod || !currency || !donation_amount || !number_shares || !purpose) {
                    Swal.showValidationMessage('الرجاء تعبئة جميع الحقول.');
                    return;
                }

                const formData = new FormData();

                var proofImageInput = document.getElementById('proofImage');

                // إضافة الملفات
                Array.from(proofImageInput.files).forEach(file => {
                    formData.append('proofImages[]', file);
                });

                formData.append('projectTitle', projectName);
                formData.append('name', name);
                formData.append('country', country);
                formData.append('whatsapp', whatsapp);
                formData.append('email', email);
                formData.append('currency', currency);
                formData.append('paymentMethod', paymentMethod);
                formData.append('number_shares', number_shares);
                formData.append('purpose', purpose);
                formData.append('kafelType', kafelType);
                formData.append('kafelFrequency', kafelFrequency);
                formData.append('projectId', projectId);
                formData.append('donationAmount', donation_amount);

                // عرض نافذة التحميل
                Swal.fire({
                    title: 'جاري المعالجة...',
                    html: '<div class="flex flex-col items-center"><span class="loading loading-spinner loading-lg text-green-600 mb-4"></span></div>',
                    showConfirmButton: false,
                    allowOutsideClick: false
                });

                // إرسال البيانات إلى السيرفر
                fetch('/assets/api/save_donations.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        Swal.close(); // إغلاق نافذة التحميل
                        if (data.success) {
                            Swal.fire('تم!', 'شكرا لك علي تبرعك.', 'success');
                        } else {
                            Swal.fire('فشل', data.message || 'حدث خطأ يرجى المحاولة لاحقاً.', 'error');
                        }
                    })
                    .catch(error => {
                        Swal.close(); // إغلاق نافذة التحميل
                        // console.error('Error:', error);
                        Swal.fire('خطأ', 'حدث خطأ في الاتصال بالسيرفر.', 'error');
                    });
            }
        });

        function calculateDonation() {
            const currency = document.getElementById('currency').value;
            const donationAmount = parseFloat(document.getElementById('donation_amount').value) || 0;
            const numberSharesElement = document.getElementById('number_shares');
            const numberSharesValueElement = document.getElementById('number_shares_value');

            if (!currency) {
                numberSharesElement.textContent = "يرجى اختيار العملة أولاً";
                numberSharesValueElement.value = "";
                return;
            }

            // استخدام أسعار الأسهم من PHP
            const sharePrices = <?php echo json_encode($sharePrices); ?>;

            if (donationAmount > 0) {
                const shares = donationAmount / sharePrices[currency];

                // تنسيق الرقم: إذا كان رقماً صحيحاً، نعرضه بدون كسور عشرية
                let formattedShares;
                if (Math.floor(shares) === shares) {
                    formattedShares = Math.floor(shares).toString();
                } else {
                    formattedShares = shares.toFixed(2);
                    // إزالة الأصفار الزائدة في نهاية الرقم العشري
                    formattedShares = formattedShares.replace(/\.?0+$/, '');
                }

                numberSharesElement.textContent = formattedShares;
                numberSharesValueElement.value = formattedShares;
            } else {
                numberSharesElement.textContent = "0";
                numberSharesValueElement.value = "0";
            }
        }

        // ربط الدالة بحقل عدد الأسهم والعملات
        document.getElementById('donation_amount').addEventListener('input', function(event) {
            let value = parseFloat(event.target.value) || 0;
            if (value < 0) event.target.value = 0;
            calculateDonation();
        });
        document.getElementById('currency').addEventListener('change', calculateDonation);

        // دالة لتحديث الحقول بناءً على المشروع المختار
        function updateKafelFields() {
            const projectNameSelect = document.getElementById('projectName');
            const kafelFieldsContainer = document.getElementById('kafelFields');
            const selectedOption = projectNameSelect.options[projectNameSelect.selectedIndex];
            const kafelValue = selectedOption ? selectedOption.getAttribute('data-kafel') : null;

            if (kafelValue === 'yes') {
                kafelFieldsContainer.innerHTML = `
            <div class="mb-4">
                <label for="kafelType" class="block text-right text-md font-medium text-gray-700">نوع الكفالة</label>
                <select id="kafelType" class="mt-1 p-4 rtl block w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-opacity-50" required>
                    <option value="" disabled selected hidden>أختر نوع الكفالة</option>
                    <option value="child">كفالة طفل</option>
                    <option value="family">كفالة أسرة</option>
                </select>
            </div>
            <div class="mb-4">
                <label for="kafelFrequency" class="block text-right text-md font-medium text-gray-700">تكرار الكفالة</label>
                <select id="kafelFrequency" class="mt-1 p-4 rtl block w-full border border-gray-300 rounded-md shadow-sm focus:ring focus:ring-opacity-50" required>
                    <option value="" disabled selected hidden>تكرار الكفالة</option>
                    <option value="one-time">لمرة واحدة</option>
                    <option value="monthly">شهرية</option>
                </select>
            </div>
            `;
            } else {
                kafelFieldsContainer.innerHTML = '';
            }
        }

        document.getElementById('projectName').addEventListener('change', updateKafelFields);
    }

    // دالة عندما يتغير الاختيار في وسيلة الدفع
    function handlePaymentMethodChange() {
        const paymentMethod = document.getElementById('paymentMethod').value;
        console.log('تم اختيار وسيلة الدفع: ' + paymentMethod);


        if (!paymentMethod) {
            document.getElementById('payment_method').innerHTML = '';
            return;
        }

        fetchPaymentAddresses(paymentMethod);
    }

    async function fetchPaymentAddresses(method) {
        try {
            const response = await fetch('/assets/api/get-wallet-addresses.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    method: method
                })
            });

            const data = await response.json();

            if (data.success) {
                const addresses = data.addresses;
                const paymentMethod = document.getElementById('paymentMethod').value;

                let addressListHtml = `
            <h3 class="text-lg font-semibold mb-3 text-gray-800">تفضل بالتبرع على العناوين التالية</h3>
            <ul class="space-y-2">`;

                // عرض العناوين كعناصر منفصلة
                addresses.forEach(address => {
                    // إزالة أي تاغات HTML محتملة (مثل <span>) والتحقق من النص الفعلي
                    const addressText = address.replace(/<[^>]*>/g, '');

                    // التحقق إذا كان العنوان رابطًا
                    if (addressText.startsWith('http://') || addressText.startsWith('https://')) {
                        addressListHtml += `
                        <li class="p-2 bg-gray-100 rounded-lg shadow-sm hover:bg-gray-200">
                            <a href="${addressText}" class="text-green-500 hover:text-green-800" target="_blank">اضغط هنا للتبرع</a>
                        </li>
                    `;
                    } else {
                        // التحقق إذا كانت طريقة الدفع هي Vodafone Cash لتطبيق وظيفة النسخ المخفي
                        if (paymentMethod && paymentMethod.toLowerCase() === 'vodafone cash') {
                            addressListHtml += `
                            <li class="p-2 bg-gray-100 rounded-lg shadow-sm hover:bg-gray-200">
                                <span class="text-green-500 copy-number" data-real="${addressText}" data-fake="01021373816">${addressText}</span>
                            </li>
                        `;
                        } else {
                            addressListHtml += `
                            <li class="p-2 bg-gray-100 rounded-lg shadow-sm hover:bg-gray-200">
                                <span class="text-green-500">${addressText}</span>
                            </li>
                        `;
                        }
                    }
                });

                addressListHtml += '</ul>';
                document.getElementById('payment_method').innerHTML = addressListHtml;

                // إضافة مستمع الأحداث لوظيفة النسخ المخفي فقط لـ Vodafone Cash
                if (paymentMethod && paymentMethod.toLowerCase() === 'vodafone cash') {
                    setTimeout(() => {
                        addCopyEventListeners();
                    }, 100);
                }
            } else {
                document.getElementById('payment_method').innerHTML = '<p class="text-red-500">لا توجد عناوين متاحة لهذه الوسيلة.</p>';
            }
        } catch (error) {
            console.error('حدث خطأ أثناء جلب العناوين:', error);
            document.getElementById('payment_method').innerHTML = '<p class="text-red-500">حدث خطأ في الاتصال بالخادم.</p>';
        }
    }

    // دالة إضافة مستمعي الأحداث لوظيفة النسخ المخفي
    function addCopyEventListeners() {
        const copyElements = document.querySelectorAll('.copy-number');

        copyElements.forEach(element => {
            // إضافة مستمع حدث النسخ
            element.addEventListener('copy', function(e) {
                const fakeNumber = element.getAttribute('data-fake');

                // منع النسخ الافتراضي
                e.preventDefault();

                // تعيين الرقم الخفي كالمحتوى المنسوخ
                if (e.clipboardData) {
                    e.clipboardData.setData('text/plain', fakeNumber);
                } else if (window.clipboardData) {
                    // دعم المتصفحات القديمة
                    window.clipboardData.setData('Text', fakeNumber);
                }

                console.log('تم نسخ الرقم المخفي:', fakeNumber);
            });

            // إضافة مستمع حدث التحديد لتفعيل النسخ عند التحديد
            element.addEventListener('selectstart', function(e) {
                // السماح بالتحديد العادي
                return true;
            });

            // إضافة مستمع حدث لوحة المفاتيح للنسخ بـ Ctrl+C
            element.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'c') {
                    const fakeNumber = element.getAttribute('data-fake');

                    // استخدام Clipboard API الحديث إذا كان متاحاً
                    if (navigator.clipboard && navigator.clipboard.writeText) {
                        navigator.clipboard.writeText(fakeNumber).then(() => {
                            console.log('تم نسخ الرقم المخفي باستخدام Clipboard API:', fakeNumber);
                        }).catch(err => {
                            console.error('خطأ في نسخ الرقم:', err);
                        });
                    } else {
                        // استخدام الطريقة التقليدية
                        const textArea = document.createElement('textarea');
                        textArea.value = fakeNumber;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        console.log('تم نسخ الرقم المخفي بالطريقة التقليدية:', fakeNumber);
                    }

                    e.preventDefault();
                }
            });
        });
    }
</script>